import os
import re
import io  # 添加io模块导入
import fitz  # PyMuPDF for PDF processing
import logging
from PIL import Image
import pytesseract
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
import cv2
import time
from datetime import datetime


def allowed_file(filename, allowed_extensions):
    return '.' in filename and \
        filename.rsplit('.', 1)[1].lower() in allowed_extensions


def extract_tables_from_page(page) -> List[str]:
    """从页面提取表格内容"""
    tables = []
    try:
        # 使用PyMuPDF提取表格
        tabs = page.find_tables()
        for tab in tabs:
            try:
                df = tab.to_pandas()
                if not df.empty:
                    # 将表格转换为文本格式
                    table_text = "表格内容：\n"
                    table_text += df.to_string(index=False, na_rep='')
                    tables.append(table_text)
            except Exception as e:
                logging.warning(f"表格提取失败: {str(e)}")
                continue
    except Exception as e:
        logging.warning(f"页面表格提取失败: {str(e)}")

    return tables


def extract_images_from_page(page) -> List[str]:
    """从页面提取图片并进行OCR"""
    image_texts = []
    try:
        image_list = page.get_images()
        for img_index, img in enumerate(image_list):
            try:
                # 获取图片
                xref = img[0]
                pix = fitz.Pixmap(page.parent, xref)

                if pix.n - pix.alpha < 4:  # 确保是RGB或灰度图
                    # 转换为PIL图像
                    img_data = pix.tobytes("ppm")
                    pil_img = Image.open(io.BytesIO(img_data))

                    # 图像预处理提高OCR准确性
                    pil_img = enhance_image_for_ocr(pil_img)

                    # OCR识别
                    ocr_text = pytesseract.image_to_string(
                        pil_img,
                        lang='chi_sim+eng',
                        config='--psm 6'
                    )

                    if ocr_text.strip():
                        image_texts.append(f"图片{img_index+1}内容：{ocr_text.strip()}")

                pix = None  # 释放内存
            except Exception as e:
                logging.warning(f"图片{img_index+1}OCR失败: {str(e)}")
                continue
    except Exception as e:
        logging.warning(f"页面图片提取失败: {str(e)}")

    return image_texts


def enhance_image_for_ocr(image: Image.Image) -> Image.Image:
    """增强图像以提高OCR准确性"""
    try:
        # 转换为numpy数组
        img_array = np.array(image)

        # 转换为灰度图
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array

        # 去噪
        denoised = cv2.medianBlur(gray, 3)

        # 自适应阈值处理
        binary = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )

        # 形态学操作去除噪点
        kernel = np.ones((1, 1), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

        # 转换回PIL图像
        enhanced_image = Image.fromarray(cleaned)
        return enhanced_image
    except Exception as e:
        logging.warning(f"图像增强失败: {str(e)}")
        return image


def process_pdf(file_path):
    """增强的PDF处理，支持表格和图片提取"""
    try:
        import io
        doc = fitz.open(file_path)
        all_content = []

        for page_num, page in enumerate(doc):
            page_content = []

            # 1. 提取常规文本
            page_text = page.get_text("text")
            if page_text.strip():
                page_content.append(page_text.strip())

            # 2. 提取表格
            tables = extract_tables_from_page(page)
            page_content.extend(tables)

            # 3. 提取图片文字（如果常规文本较少）
            if len(page_text) < 100:
                try:
                    # 整页OCR
                    pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 提高分辨率
                    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                    img = enhance_image_for_ocr(img)
                    ocr_text = pytesseract.image_to_string(img, lang='chi_sim+eng')
                    if ocr_text.strip():
                        page_content.append(f"页面{page_num+1}OCR内容：{ocr_text.strip()}")
                except Exception as ocr_error:
                    logging.warning(f"页面{page_num+1}OCR失败: {str(ocr_error)}")

            # 4. 提取独立图片
            image_texts = extract_images_from_page(page)
            page_content.extend(image_texts)

            # 合并页面内容
            if page_content:
                page_combined = f"\n\n=== 第{page_num+1}页 ===\n\n" + "\n\n".join(page_content)
                all_content.append(page_combined)

        doc.close()

        # 合并所有内容
        full_text = "\n\n".join(all_content)

        # 清理文本但保持结构
        full_text = re.sub(r'\n\s*\n\s*\n', '\n\n', full_text)  # 减少多余空行
        full_text = re.sub(r'[ \t]+', ' ', full_text)  # 清理空格和制表符

        return full_text.strip()
    except Exception as e:
        raise Exception(f"处理PDF错误: {str(e)}")


def process_pdf_with_stats(file_path):
    """增强的PDF处理，返回文本和统计信息"""
    try:
        doc = fitz.open(file_path)
        all_content = []
        stats = {
            "pages_count": len(doc),
            "tables_count": 0,
            "images_count": 0,
            "ocr_pages": 0,
            "text_pages": 0,
            "processing_errors": []
        }

        for page_num, page in enumerate(doc):
            page_content = []
            page_stats = {"tables": 0, "images": 0, "has_text": False, "ocr_used": False}

            try:
                # 1. 提取常规文本
                page_text = page.get_text("text")
                if page_text.strip():
                    page_content.append(page_text.strip())
                    page_stats["has_text"] = True
                    stats["text_pages"] += 1

                # 2. 提取表格
                tables = extract_tables_from_page(page)
                if tables:
                    page_content.extend(tables)
                    page_stats["tables"] = len(tables)
                    stats["tables_count"] += len(tables)

                # 3. 提取图片文字（如果常规文本较少）
                if len(page_text) < 100:
                    try:
                        # 整页OCR
                        pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 提高分辨率
                        img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                        img = enhance_image_for_ocr(img)
                        ocr_text = pytesseract.image_to_string(img, lang='chi_sim+eng')
                        if ocr_text.strip():
                            page_content.append(f"页面{page_num+1}OCR内容：{ocr_text.strip()}")
                            page_stats["ocr_used"] = True
                            stats["ocr_pages"] += 1
                    except Exception as ocr_error:
                        error_msg = f"页面{page_num+1}OCR失败: {str(ocr_error)}"
                        logging.warning(error_msg)
                        stats["processing_errors"].append(error_msg)

                # 4. 提取独立图片
                image_texts = extract_images_from_page(page)
                if image_texts:
                    page_content.extend(image_texts)
                    page_stats["images"] = len(image_texts)
                    stats["images_count"] += len(image_texts)

                # 合并页面内容
                if page_content:
                    page_combined = f"\n\n=== 第{page_num+1}页 ===\n\n" + "\n\n".join(page_content)
                    all_content.append(page_combined)

                # 记录页面处理详情
                logging.info(f"页面{page_num+1}: 文本={page_stats['has_text']}, "
                           f"表格={page_stats['tables']}, 图片={page_stats['images']}, "
                           f"OCR={page_stats['ocr_used']}")

            except Exception as page_error:
                error_msg = f"页面{page_num+1}处理失败: {str(page_error)}"
                logging.error(error_msg)
                stats["processing_errors"].append(error_msg)

        doc.close()

        # 合并所有内容
        full_text = "\n\n".join(all_content)

        # 清理文本但保持结构
        full_text = re.sub(r'\n\s*\n\s*\n', '\n\n', full_text)
        full_text = re.sub(r'[ \t]+', ' ', full_text)

        # 更新统计信息
        stats["total_characters"] = len(full_text)
        stats["success_rate"] = (stats["pages_count"] - len(stats["processing_errors"])) / stats["pages_count"] * 100

        logging.info(f"PDF处理完成: {stats['pages_count']}页, "
                    f"表格{stats['tables_count']}个, 图片{stats['images_count']}个, "
                    f"OCR页面{stats['ocr_pages']}个, 成功率{stats['success_rate']:.1f}%")

        return full_text.strip(), stats

    except Exception as e:
        raise Exception(f"处理PDF错误: {str(e)}")


def process_txt_with_stats(file_path):
    """处理TXT文件并返回统计信息"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            text = f.read()

        # 清理文本
        original_length = len(text)
        text = re.sub(r'\s+', ' ', text).strip()
        cleaned_length = len(text)

        # 统计信息
        stats = {
            "pages_count": 1,
            "tables_count": 0,
            "images_count": 0,
            "ocr_pages": 0,
            "text_pages": 1,
            "processing_errors": [],
            "original_length": original_length,
            "cleaned_length": cleaned_length,
            "compression_ratio": (original_length - cleaned_length) / original_length * 100 if original_length > 0 else 0,
            "total_characters": cleaned_length,
            "success_rate": 100.0
        }

        # 检测可能的表格内容
        if re.search(r'\t.*\t', text) or re.search(r'\|.*\|', text):
            stats["tables_count"] = len(re.findall(r'(\t.*\t|\|.*\|)', text))

        logging.info(f"TXT处理完成: {stats['total_characters']}字符, "
                    f"压缩率{stats['compression_ratio']:.1f}%")

        return text, stats

    except Exception as e:
        raise Exception(f"处理TXT错误: {str(e)}")


def process_txt(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            text = f.read()
        # 清理文本
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    except Exception as e:
        raise Exception(f"处理TXT错误: {str(e)}")


def semantic_chunk_text(text, min_chunk_size=300, max_chunk_size=800, overlap=100):
    """
    语义感知的文本分块，保持内容连贯性

    参数:
        text: 原始文本
        min_chunk_size: 最小分块大小(字符数)
        max_chunk_size: 最大分块大小(字符数)
        overlap: 重叠区域大小(字符数)

    返回:
        分块后的文本列表
    """
    # 识别不同类型的内容分隔符
    section_patterns = [
        r'\n\s*=+\s*第\d+[页章节]\s*=+\s*\n',  # 页面/章节分隔
        r'\n\s*第[一二三四五六七八九十\d]+[章节部分]\s*[：:]\s*',  # 章节标题
        r'\n\s*\d+\.\s*',  # 数字列表
        r'\n\s*[（(]\d+[）)]\s*',  # 括号数字
        r'\n\s*表格内容：\s*\n',  # 表格标识
        r'\n\s*图片\d+内容：',  # 图片标识
    ]

    # 按主要分隔符分割
    sections = [text]
    for pattern in section_patterns:
        new_sections = []
        for section in sections:
            parts = re.split(pattern, section)
            if len(parts) > 1:
                # 保留分隔符
                matches = re.findall(pattern, section)
                combined = []
                for i, part in enumerate(parts):
                    if part.strip():
                        combined.append(part.strip())
                    if i < len(matches):
                        combined.append(matches[i].strip())
                new_sections.extend(combined)
            else:
                new_sections.append(section)
        sections = [s for s in new_sections if s.strip()]

    # 智能合并小段落
    chunks = []
    current_chunk = ""

    for section in sections:
        section = section.strip()
        if not section:
            continue

        # 特殊内容类型处理
        is_table = "表格内容：" in section
        is_image = "图片" in section and "内容：" in section
        is_title = any(pattern in section for pattern in ["第", "章", "节", "部分"])

        # 如果是表格或图片，尝试作为独立块
        if is_table or is_image:
            if len(section) >= min_chunk_size:
                if current_chunk:
                    chunks.append(current_chunk)
                    current_chunk = ""
                chunks.append(section)
                continue

        # 检查是否可以添加到当前块
        potential_length = len(current_chunk) + len(section) + 2

        if potential_length <= max_chunk_size:
            if current_chunk:
                current_chunk += "\n\n" + section
            else:
                current_chunk = section
        else:
            # 当前块已满，保存并开始新块
            if current_chunk and len(current_chunk) >= min_chunk_size:
                chunks.append(current_chunk)

                # 创建重叠
                if overlap > 0:
                    overlap_text = current_chunk[-overlap:] if len(current_chunk) > overlap else current_chunk
                    current_chunk = overlap_text + "\n\n" + section
                else:
                    current_chunk = section
            else:
                # 当前块太小，继续添加
                if len(section) > max_chunk_size:
                    # 长段落需要分割
                    if current_chunk:
                        chunks.append(current_chunk)

                    # 分割长段落
                    long_chunks = split_long_text(section, max_chunk_size, overlap)
                    chunks.extend(long_chunks[:-1])
                    current_chunk = long_chunks[-1] if long_chunks else ""
                else:
                    current_chunk = section

    # 添加最后一个块
    if current_chunk and len(current_chunk.strip()) > 0:
        chunks.append(current_chunk)

    # 过滤太短的块
    filtered_chunks = []
    for chunk in chunks:
        if len(chunk.strip()) >= min_chunk_size or any(keyword in chunk for keyword in ["表格", "图片"]):
            filtered_chunks.append(chunk.strip())

    return filtered_chunks if filtered_chunks else [text]


def split_long_text(text, max_size, overlap):
    """分割过长的文本"""
    if len(text) <= max_size:
        return [text]

    chunks = []
    start = 0

    while start < len(text):
        end = min(start + max_size, len(text))

        # 尝试在句号、问号、感叹号处分割
        if end < len(text):
            for i in range(end, max(start + max_size // 2, start + 1), -1):
                if text[i-1] in '。！？\n':
                    end = i
                    break

        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)

        # 设置下一个块的起始位置（考虑重叠）
        start = max(end - overlap, start + 1)
        if start >= len(text):
            break

    return chunks


def analyze_chunks(chunks):
    """分析文本分块的统计信息"""
    if not chunks:
        return {
            "avg_length": 0,
            "min_length": 0,
            "max_length": 0,
            "total_length": 0,
            "text_chunks": 0,
            "table_chunks": 0,
            "image_chunks": 0
        }

    lengths = [len(chunk) for chunk in chunks]

    # 分类统计
    text_chunks = 0
    table_chunks = 0
    image_chunks = 0

    for chunk in chunks:
        if "表格内容：" in chunk:
            table_chunks += 1
        elif "图片" in chunk and "内容：" in chunk:
            image_chunks += 1
        else:
            text_chunks += 1

    return {
        "avg_length": sum(lengths) // len(lengths),
        "min_length": min(lengths),
        "max_length": max(lengths),
        "total_length": sum(lengths),
        "text_chunks": text_chunks,
        "table_chunks": table_chunks,
        "image_chunks": image_chunks,
        "length_distribution": {
            "small": sum(1 for l in lengths if l < 300),
            "medium": sum(1 for l in lengths if 300 <= l <= 600),
            "large": sum(1 for l in lengths if l > 600)
        }
    }


class ProcessingProgress:
    """文件处理进度跟踪器"""
    def __init__(self, filename):
        self.filename = filename
        self.start_time = time.time()
        self.steps = []
        self.current_step = 0
        self.total_steps = 0

    def add_step(self, step_name, description=""):
        """添加处理步骤"""
        self.steps.append({
            "name": step_name,
            "description": description,
            "status": "pending",
            "start_time": None,
            "end_time": None,
            "duration": 0,
            "details": {}
        })
        self.total_steps = len(self.steps)

    def start_step(self, step_index, details=None):
        """开始执行步骤"""
        if 0 <= step_index < len(self.steps):
            self.current_step = step_index
            self.steps[step_index]["status"] = "running"
            self.steps[step_index]["start_time"] = time.time()
            if details:
                self.steps[step_index]["details"].update(details)
            logging.info(f"开始执行: {self.steps[step_index]['name']}")

    def complete_step(self, step_index, details=None):
        """完成步骤"""
        if 0 <= step_index < len(self.steps):
            step = self.steps[step_index]
            step["status"] = "completed"
            step["end_time"] = time.time()
            if step["start_time"]:
                step["duration"] = step["end_time"] - step["start_time"]
            if details:
                step["details"].update(details)
            logging.info(f"完成步骤: {step['name']} (耗时: {step['duration']:.2f}秒)")

    def fail_step(self, step_index, error_msg, details=None):
        """步骤失败"""
        if 0 <= step_index < len(self.steps):
            step = self.steps[step_index]
            step["status"] = "failed"
            step["end_time"] = time.time()
            if step["start_time"]:
                step["duration"] = step["end_time"] - step["start_time"]
            step["error"] = error_msg
            if details:
                step["details"].update(details)
            logging.error(f"步骤失败: {step['name']} - {error_msg}")

    def get_progress(self):
        """获取当前进度"""
        completed = sum(1 for step in self.steps if step["status"] == "completed")
        failed = sum(1 for step in self.steps if step["status"] == "failed")

        return {
            "filename": self.filename,
            "total_steps": self.total_steps,
            "completed_steps": completed,
            "failed_steps": failed,
            "current_step": self.current_step,
            "progress_percentage": (completed / self.total_steps * 100) if self.total_steps > 0 else 0,
            "elapsed_time": time.time() - self.start_time,
            "steps": self.steps
        }


def process_uploaded_file(file, upload_folder, collection, embedding_generator, progress_callback=None):
    """处理上传的文件，使用增强的分块策略和可视化进度"""
    filename = os.path.join(upload_folder, file.filename)

    # 创建进度跟踪器
    progress = ProcessingProgress(file.filename)

    # 定义处理步骤
    progress.add_step("file_save", "保存上传文件")
    progress.add_step("file_parse", "解析文件内容")
    progress.add_step("content_extract", "提取文本、表格和图片")
    progress.add_step("text_chunk", "智能文本分块")
    progress.add_step("generate_embeddings", "生成向量嵌入")
    progress.add_step("store_vectors", "存储到向量数据库")
    progress.add_step("cleanup", "清理临时文件")

    try:
        # 步骤1: 保存文件
        progress.start_step(0)
        file.save(filename)
        file_size = os.path.getsize(filename)
        progress.complete_step(0, {"file_size": file_size})

        if progress_callback:
            progress_callback(progress.get_progress())

        # 步骤2: 解析文件
        progress.start_step(1)
        file_type = "PDF" if filename.lower().endswith('.pdf') else "TXT"
        progress.complete_step(1, {"file_type": file_type})

        if progress_callback:
            progress_callback(progress.get_progress())

        # 步骤3: 提取内容
        progress.start_step(2)
        extraction_start = time.time()

        if filename.lower().endswith('.pdf'):
            text, extraction_stats = process_pdf_with_stats(filename)
        else:
            text, extraction_stats = process_txt_with_stats(filename)

        extraction_time = time.time() - extraction_start
        progress.complete_step(2, {
            "extraction_time": extraction_time,
            "text_length": len(text),
            **extraction_stats
        })

        if progress_callback:
            progress_callback(progress.get_progress())

        # 步骤4: 文本分块
        progress.start_step(3)
        chunk_start = time.time()
        chunks = semantic_chunk_text(text, min_chunk_size=300, max_chunk_size=800, overlap=100)
        chunk_time = time.time() - chunk_start

        # 分析分块统计
        chunk_stats = analyze_chunks(chunks)

        progress.complete_step(3, {
            "chunk_time": chunk_time,
            "chunks_count": len(chunks),
            **chunk_stats
        })

        if progress_callback:
            progress_callback(progress.get_progress())

        # 步骤5: 生成嵌入向量
        progress.start_step(4)
        embedding_start = time.time()
        embeddings = embedding_generator(chunks)
        embedding_time = time.time() - embedding_start

        progress.complete_step(4, {
            "embedding_time": embedding_time,
            "embedding_dimension": len(embeddings[0]) if embeddings else 0,
            "vectors_count": len(embeddings)
        })

        if progress_callback:
            progress_callback(progress.get_progress())

        # 步骤6: 存储向量
        progress.start_step(5)
        storage_start = time.time()
        from milvus_manager import store_vectors
        storage_result = store_vectors(chunks, embeddings, collection)
        storage_time = time.time() - storage_start

        progress.complete_step(5, {
            "storage_time": storage_time,
            "storage_result": storage_result
        })

        if progress_callback:
            progress_callback(progress.get_progress())

        # 步骤7: 清理
        progress.start_step(6)
        os.remove(filename)
        progress.complete_step(6)

        if progress_callback:
            progress_callback(progress.get_progress())

        # 生成最终报告
        final_progress = progress.get_progress()
        total_time = final_progress["elapsed_time"]

        return {
            "status": "success",
            "message": f"文件处理成功，已存储 {len(chunks)} 个片段到向量数据库",
            "processing_time": total_time,
            "chunks_count": len(chunks),
            "file_size": len(text),
            "processing_details": {
                "file_type": file_type,
                "has_tables": extraction_stats.get("tables_count", 0) > 0,
                "has_images": extraction_stats.get("images_count", 0) > 0,
                "tables_count": extraction_stats.get("tables_count", 0),
                "images_count": extraction_stats.get("images_count", 0),
                "pages_count": extraction_stats.get("pages_count", 1),
                "avg_chunk_size": chunk_stats.get("avg_length", 0),
                "embedding_dimension": len(embeddings[0]) if embeddings else 0
            },
            "processing_steps": final_progress["steps"],
            "performance_metrics": {
                "extraction_time": extraction_time,
                "chunking_time": chunk_time,
                "embedding_time": embedding_time,
                "storage_time": storage_time,
                "total_time": total_time,
                "chunks_per_second": len(chunks) / total_time if total_time > 0 else 0
            }
        }

    except Exception as e:
        # 记录失败步骤
        if progress.current_step < len(progress.steps):
            progress.fail_step(progress.current_step, str(e))

        # 确保删除临时文件
        if os.path.exists(filename):
            os.remove(filename)

        # 返回错误信息和进度
        return {
            "status": "error",
            "message": f"文件处理失败: {str(e)}",
            "processing_steps": progress.get_progress()["steps"],
            "error_step": progress.current_step
        }