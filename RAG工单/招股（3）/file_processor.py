import os
import re
import fitz  # PyMuPDF for PDF processing
import logging
from PIL import Image
import pytesseract
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
import cv2


def allowed_file(filename, allowed_extensions):
    return '.' in filename and \
        filename.rsplit('.', 1)[1].lower() in allowed_extensions


def extract_tables_from_page(page) -> List[str]:
    """从页面提取表格内容"""
    tables = []
    try:
        # 使用PyMuPDF提取表格
        tabs = page.find_tables()
        for tab in tabs:
            try:
                df = tab.to_pandas()
                if not df.empty:
                    # 将表格转换为文本格式
                    table_text = "表格内容：\n"
                    table_text += df.to_string(index=False, na_rep='')
                    tables.append(table_text)
            except Exception as e:
                logging.warning(f"表格提取失败: {str(e)}")
                continue
    except Exception as e:
        logging.warning(f"页面表格提取失败: {str(e)}")

    return tables


def extract_images_from_page(page) -> List[str]:
    """从页面提取图片并进行OCR"""
    image_texts = []
    try:
        image_list = page.get_images()
        for img_index, img in enumerate(image_list):
            try:
                # 获取图片
                xref = img[0]
                pix = fitz.Pixmap(page.parent, xref)

                if pix.n - pix.alpha < 4:  # 确保是RGB或灰度图
                    # 转换为PIL图像
                    img_data = pix.tobytes("ppm")
                    pil_img = Image.open(io.BytesIO(img_data))

                    # 图像预处理提高OCR准确性
                    pil_img = enhance_image_for_ocr(pil_img)

                    # OCR识别
                    ocr_text = pytesseract.image_to_string(
                        pil_img,
                        lang='chi_sim+eng',
                        config='--psm 6'
                    )

                    if ocr_text.strip():
                        image_texts.append(f"图片{img_index+1}内容：{ocr_text.strip()}")

                pix = None  # 释放内存
            except Exception as e:
                logging.warning(f"图片{img_index+1}OCR失败: {str(e)}")
                continue
    except Exception as e:
        logging.warning(f"页面图片提取失败: {str(e)}")

    return image_texts


def enhance_image_for_ocr(image: Image.Image) -> Image.Image:
    """增强图像以提高OCR准确性"""
    try:
        # 转换为numpy数组
        img_array = np.array(image)

        # 转换为灰度图
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array

        # 去噪
        denoised = cv2.medianBlur(gray, 3)

        # 自适应阈值处理
        binary = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )

        # 形态学操作去除噪点
        kernel = np.ones((1, 1), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

        # 转换回PIL图像
        enhanced_image = Image.fromarray(cleaned)
        return enhanced_image
    except Exception as e:
        logging.warning(f"图像增强失败: {str(e)}")
        return image


def process_pdf(file_path):
    """增强的PDF处理，支持表格和图片提取"""
    try:
        import io
        doc = fitz.open(file_path)
        all_content = []

        for page_num, page in enumerate(doc):
            page_content = []

            # 1. 提取常规文本
            page_text = page.get_text("text")
            if page_text.strip():
                page_content.append(page_text.strip())

            # 2. 提取表格
            tables = extract_tables_from_page(page)
            page_content.extend(tables)

            # 3. 提取图片文字（如果常规文本较少）
            if len(page_text) < 100:
                try:
                    # 整页OCR
                    pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 提高分辨率
                    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                    img = enhance_image_for_ocr(img)
                    ocr_text = pytesseract.image_to_string(img, lang='chi_sim+eng')
                    if ocr_text.strip():
                        page_content.append(f"页面{page_num+1}OCR内容：{ocr_text.strip()}")
                except Exception as ocr_error:
                    logging.warning(f"页面{page_num+1}OCR失败: {str(ocr_error)}")

            # 4. 提取独立图片
            image_texts = extract_images_from_page(page)
            page_content.extend(image_texts)

            # 合并页面内容
            if page_content:
                page_combined = f"\n\n=== 第{page_num+1}页 ===\n\n" + "\n\n".join(page_content)
                all_content.append(page_combined)

        doc.close()

        # 合并所有内容
        full_text = "\n\n".join(all_content)

        # 清理文本但保持结构
        full_text = re.sub(r'\n\s*\n\s*\n', '\n\n', full_text)  # 减少多余空行
        full_text = re.sub(r'[ \t]+', ' ', full_text)  # 清理空格和制表符

        return full_text.strip()
    except Exception as e:
        raise Exception(f"处理PDF错误: {str(e)}")


def process_txt(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            text = f.read()
        # 清理文本
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    except Exception as e:
        raise Exception(f"处理TXT错误: {str(e)}")


def semantic_chunk_text(text, min_chunk_size=300, max_chunk_size=800, overlap=100):
    """
    语义感知的文本分块，保持内容连贯性

    参数:
        text: 原始文本
        min_chunk_size: 最小分块大小(字符数)
        max_chunk_size: 最大分块大小(字符数)
        overlap: 重叠区域大小(字符数)

    返回:
        分块后的文本列表
    """
    # 识别不同类型的内容分隔符
    section_patterns = [
        r'\n\s*=+\s*第\d+[页章节]\s*=+\s*\n',  # 页面/章节分隔
        r'\n\s*第[一二三四五六七八九十\d]+[章节部分]\s*[：:]\s*',  # 章节标题
        r'\n\s*\d+\.\s*',  # 数字列表
        r'\n\s*[（(]\d+[）)]\s*',  # 括号数字
        r'\n\s*表格内容：\s*\n',  # 表格标识
        r'\n\s*图片\d+内容：',  # 图片标识
    ]

    # 按主要分隔符分割
    sections = [text]
    for pattern in section_patterns:
        new_sections = []
        for section in sections:
            parts = re.split(pattern, section)
            if len(parts) > 1:
                # 保留分隔符
                matches = re.findall(pattern, section)
                combined = []
                for i, part in enumerate(parts):
                    if part.strip():
                        combined.append(part.strip())
                    if i < len(matches):
                        combined.append(matches[i].strip())
                new_sections.extend(combined)
            else:
                new_sections.append(section)
        sections = [s for s in new_sections if s.strip()]

    # 智能合并小段落
    chunks = []
    current_chunk = ""

    for section in sections:
        section = section.strip()
        if not section:
            continue

        # 特殊内容类型处理
        is_table = "表格内容：" in section
        is_image = "图片" in section and "内容：" in section
        is_title = any(pattern in section for pattern in ["第", "章", "节", "部分"])

        # 如果是表格或图片，尝试作为独立块
        if is_table or is_image:
            if len(section) >= min_chunk_size:
                if current_chunk:
                    chunks.append(current_chunk)
                    current_chunk = ""
                chunks.append(section)
                continue

        # 检查是否可以添加到当前块
        potential_length = len(current_chunk) + len(section) + 2

        if potential_length <= max_chunk_size:
            if current_chunk:
                current_chunk += "\n\n" + section
            else:
                current_chunk = section
        else:
            # 当前块已满，保存并开始新块
            if current_chunk and len(current_chunk) >= min_chunk_size:
                chunks.append(current_chunk)

                # 创建重叠
                if overlap > 0:
                    overlap_text = current_chunk[-overlap:] if len(current_chunk) > overlap else current_chunk
                    current_chunk = overlap_text + "\n\n" + section
                else:
                    current_chunk = section
            else:
                # 当前块太小，继续添加
                if len(section) > max_chunk_size:
                    # 长段落需要分割
                    if current_chunk:
                        chunks.append(current_chunk)

                    # 分割长段落
                    long_chunks = split_long_text(section, max_chunk_size, overlap)
                    chunks.extend(long_chunks[:-1])
                    current_chunk = long_chunks[-1] if long_chunks else ""
                else:
                    current_chunk = section

    # 添加最后一个块
    if current_chunk and len(current_chunk.strip()) > 0:
        chunks.append(current_chunk)

    # 过滤太短的块
    filtered_chunks = []
    for chunk in chunks:
        if len(chunk.strip()) >= min_chunk_size or any(keyword in chunk for keyword in ["表格", "图片"]):
            filtered_chunks.append(chunk.strip())

    return filtered_chunks if filtered_chunks else [text]


def split_long_text(text, max_size, overlap):
    """分割过长的文本"""
    if len(text) <= max_size:
        return [text]

    chunks = []
    start = 0

    while start < len(text):
        end = min(start + max_size, len(text))

        # 尝试在句号、问号、感叹号处分割
        if end < len(text):
            for i in range(end, max(start + max_size // 2, start + 1), -1):
                if text[i-1] in '。！？\n':
                    end = i
                    break

        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)

        # 设置下一个块的起始位置（考虑重叠）
        start = max(end - overlap, start + 1)
        if start >= len(text):
            break

    return chunks


def process_uploaded_file(file, upload_folder, collection, embedding_generator):
    """处理上传的文件，使用增强的分块策略"""
    # 保存文件
    filename = os.path.join(upload_folder, file.filename)
    file.save(filename)

    try:
        # 处理文件
        if filename.lower().endswith('.pdf'):
            text = process_pdf(filename)
        else:  # .txt
            text = process_txt(filename)

        # 使用语义感知分块 (新算法)
        chunks = semantic_chunk_text(text, min_chunk_size=300, max_chunk_size=800, overlap=100)

        # 记录分块信息
        logging.info(f"文件 {file.filename} 分块完成：{len(chunks)} 个片段")
        for i, chunk in enumerate(chunks[:3]):  # 记录前3个块的信息
            logging.info(f"块 {i+1} 长度: {len(chunk)} 字符")

        # 生成嵌入向量
        embeddings = embedding_generator(chunks)

        # 存储向量
        from milvus_manager import store_vectors
        result = store_vectors(chunks, embeddings, collection)

        # 删除临时文件
        os.remove(filename)

        return {
            "status": "success",
            "message": f"文件处理成功，已存储 {len(chunks)} 个片段（包含表格和图片内容）",
            "chunks_count": len(chunks),
            "file_size": len(text),
            "processing_details": {
                "has_tables": "表格内容：" in text,
                "has_images": "图片" in text and "内容：" in text,
                "avg_chunk_size": sum(len(c) for c in chunks) // len(chunks) if chunks else 0
            }
        }
    except Exception as e:
        # 确保删除临时文件
        if os.path.exists(filename):
            os.remove(filename)
        raise e