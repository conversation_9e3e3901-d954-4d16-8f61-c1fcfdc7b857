import logging
import numpy as np
import re
from typing import List, Dict, Tuple
from pymilvus import Collection
from collections import Counter
import jieba
import jieba.analyse


def extract_keywords(text: str, top_k: int = 10) -> List[str]:
    """提取关键词用于混合检索"""
    try:
        # 使用jieba提取关键词
        keywords = jieba.analyse.extract_tags(text, topK=top_k, withWeight=False)
        return keywords
    except Exception as e:
        logging.warning(f"关键词提取失败: {str(e)}")
        return []


def keyword_match_score(query: str, text: str) -> float:
    """计算关键词匹配分数"""
    try:
        query_keywords = set(extract_keywords(query, top_k=5))
        text_keywords = set(extract_keywords(text, top_k=10))

        if not query_keywords:
            return 0.0

        # 计算交集比例
        intersection = query_keywords.intersection(text_keywords)
        keyword_score = len(intersection) / len(query_keywords)

        # 考虑直接文本匹配
        query_lower = query.lower()
        text_lower = text.lower()
        direct_matches = sum(1 for word in query_keywords if word.lower() in text_lower)
        direct_score = direct_matches / len(query_keywords) if query_keywords else 0

        # 综合评分
        return max(keyword_score, direct_score) * 0.3  # 关键词权重为0.3
    except Exception as e:
        logging.warning(f"关键词匹配计算失败: {str(e)}")
        return 0.0


def semantic_similarity_boost(query: str, text: str) -> float:
    """基于语义相似性的加权"""
    try:
        # 检查特殊内容类型
        boost_score = 0.0

        # 表格内容加权
        if "表格内容：" in text and any(keyword in query for keyword in ["表", "数据", "统计", "金额", "比例"]):
            boost_score += 0.1

        # 图片内容加权
        if "图片" in text and "内容：" in text and any(keyword in query for keyword in ["图", "图表", "图像"]):
            boost_score += 0.1

        # 标题内容加权
        if any(marker in text for marker in ["第", "章", "节", "部分"]) and len(text) < 200:
            boost_score += 0.05

        return boost_score
    except Exception as e:
        logging.warning(f"语义相似性加权失败: {str(e)}")
        return 0.0


def search_similar(query, collection: Collection, embedding_generator, top_k=15):
    """增强的混合检索：向量检索 + 关键词匹配 + 语义加权"""
    try:
        # 1. 向量检索
        q_emb = embedding_generator([query])
        if isinstance(q_emb, list):
            q_emb = np.array(q_emb, dtype="float32")

        # 优化搜索参数
        search_params = {"metric_type": "IP", "params": {"ef": 256}}  # 提高ef值
        res = collection.search(
            data=[q_emb[0].tolist()],
            anns_field="vector",
            param=search_params,
            limit=min(top_k * 2, 30),  # 获取更多候选结果
            output_fields=["text"]
        )

        # 2. 混合评分
        enhanced_results = []
        for hit in res[0]:
            text = hit.entity.get("text", "")
            vector_score = float(hit.score)

            # 关键词匹配分数
            keyword_score = keyword_match_score(query, text)

            # 语义相似性加权
            semantic_boost = semantic_similarity_boost(query, text)

            # 综合评分：向量相似度(70%) + 关键词匹配(20%) + 语义加权(10%)
            final_score = vector_score * 0.7 + keyword_score * 0.2 + semantic_boost * 0.1

            enhanced_results.append({
                "text": text,
                "score": final_score,
                "id": hit.id,
                "vector_score": vector_score,
                "keyword_score": keyword_score,
                "semantic_boost": semantic_boost
            })

        # 3. 按综合分数重新排序
        enhanced_results.sort(key=lambda x: x["score"], reverse=True)

        # 4. 返回top_k结果
        return enhanced_results[:top_k]

    except Exception as e:
        logging.error("Enhanced vector search error", exc_info=True)
        return []


def search_with_filters(query: str, collection: Collection, embedding_generator,
                       content_type: str = None, top_k: int = 15) -> List[Dict]:
    """带内容类型过滤的搜索"""
    try:
        # 先进行常规搜索
        results = search_similar(query, collection, embedding_generator, top_k * 2)

        if not content_type:
            return results[:top_k]

        # 根据内容类型过滤
        filtered_results = []
        for result in results:
            text = result["text"]

            if content_type == "table" and "表格内容：" in text:
                filtered_results.append(result)
            elif content_type == "image" and "图片" in text and "内容：" in text:
                filtered_results.append(result)
            elif content_type == "text" and "表格内容：" not in text and not ("图片" in text and "内容：" in text):
                filtered_results.append(result)
            elif content_type == "all":
                filtered_results.append(result)

        return filtered_results[:top_k]

    except Exception as e:
        logging.error(f"Filtered search error: {str(e)}")
        return search_similar(query, collection, embedding_generator, top_k)