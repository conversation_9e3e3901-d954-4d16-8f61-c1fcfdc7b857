import logging
import numpy as np
from pymilvus import Collection

def search_similar(query, collection: Collection, embedding_generator, top_k=10):
    try:
        q_emb = embedding_generator([query])
        if isinstance(q_emb, list):
            q_emb = np.array(q_emb, dtype="float32")

        search_params = {"metric_type": "IP", "params": {"ef": 128}}
        res = collection.search(
            data=[q_emb[0].tolist()],
            anns_field="vector",
            param=search_params,
            limit=top_k,
            output_fields=["text"]
        )
        return [
            {"text": hit.entity.get("text"), "score": float(hit.score), "id": hit.id}
            for hit in res[0]
        ]
    except Exception as e:
        logging.error("Vector search error", exc_info=True)
        return []