import os, uuid, logging, time
from flask import Flask, request, jsonify, render_template, session, send_file
from flask_cors import CORS
from file_processor import allowed_file, process_uploaded_file
from vector_search import search_similar, search_with_filters
from llm_client import generate_answer, init_llm_client
from embeddings import generate_embeddings, init_embedding_model
from milvus_manager import init_milvus, store_vectors
from reranker import rerank_documents
from redis_manager import redis_manager
from speech_service import speech_service
import tempfile
import base64

app = Flask(__name__)
CORS(app)
app.secret_key = os.urandom(24)

UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'pdf'}
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

collection = None
llm_client = None

def initialize_application():
    global collection, llm_client
    try:
        logger.info("Initializing components...")
        collection = init_milvus()
        init_embedding_model()
        llm_client = init_llm_client()
        logger.info("All components initialized.")
        return True
    except Exception as e:
        logger.error(f"Init failed: {e}", exc_info=True)
        return False

initialize_application()

@app.route('/')
def index():
    session.setdefault("session_id", str(uuid.uuid4()))
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({"status": "error", "message": "No file part"})
    file = request.files['file']
    if file.filename == '':
        return jsonify({"status": "error", "message": "No selected file"})
    if file and allowed_file(file.filename, ALLOWED_EXTENSIONS):
        try:
            res = process_uploaded_file(file, UPLOAD_FOLDER, collection, generate_embeddings)
            return jsonify(res)
        except Exception as e:
            logger.exception("Upload error")
            return jsonify({"status": "error", "message": str(e)})
    return jsonify({"status": "error", "message": "Unsupported file type"})

@app.route('/query', methods=['POST'])
def handle_query():
    data = request.json
    if not data or "query" not in data:
        return jsonify({"status": "error", "message": "Missing query"})

    session_id = session.setdefault("session_id", str(uuid.uuid4()))
    chat_history = redis_manager.get_session(session_id)
    query = data["query"]
    content_type = data.get("content_type", "all")  # 支持内容类型过滤
    start = time.time()

    try:
        # 使用增强的混合检索
        initial = search_with_filters(query, collection, generate_embeddings, content_type, top_k=20)
        if not initial:
            no_result_msg = "未找到相关信息，请先上传文档。"
            new_hist = chat_history + [
                {"role": "user", "content": query},
                {"role": "assistant", "content": no_result_msg}
            ]
            redis_manager.store_session(session_id, new_hist)
            return jsonify({
                "status": "success",
                "answer": no_result_msg,
                "references": [],
                "session_info": redis_manager.get_session_info(session_id)
            })

        # 重排序
        reranked = rerank_documents(query, initial, top_n=6)

        # 生成答案（包含对话历史）
        answer = generate_answer(query, reranked, llm_client, chat_history)

        # 更新对话历史
        new_hist = chat_history + [
            {"role": "user", "content": query},
            {"role": "assistant", "content": answer}
        ]
        redis_manager.store_session(session_id, new_hist)

        latency = round((time.time() - start), 2)

        # 增强的参考信息
        references = []
        for r in reranked:
            ref_info = {
                "text": r["text"][:500] + "..." if len(r["text"]) > 500 else r["text"],
                "score": round(r["score"], 3),
                "type": "table" if "表格内容：" in r["text"] else
                        "image" if "图片" in r["text"] and "内容：" in r["text"] else "text"
            }
            if "vector_score" in r:
                ref_info["vector_score"] = round(r["vector_score"], 3)
                ref_info["keyword_score"] = round(r["keyword_score"], 3)
            references.append(ref_info)

        return jsonify({
            "status": "success",
            "answer": answer,
            "latency": latency,
            "references": references,
            "session_info": redis_manager.get_session_info(session_id),
            "search_stats": {
                "initial_results": len(initial),
                "reranked_results": len(reranked),
                "content_type": content_type
            }
        })
    except Exception as e:
        logger.exception("Query error")
        return jsonify({"status": "error", "message": str(e)})

# 语音相关API
@app.route('/speech-to-text', methods=['POST'])
def speech_to_text():
    """语音转文字"""
    try:
        if 'audio' not in request.files:
            return jsonify({"status": "error", "message": "No audio file"})

        audio_file = request.files['audio']
        audio_format = request.form.get('format', 'wav')

        # 读取音频数据
        audio_data = audio_file.read()

        # 验证音频格式
        if not speech_service.validate_audio_format(audio_data, audio_format):
            return jsonify({"status": "error", "message": "Invalid audio format"})

        # 语音转文字
        text = speech_service.speech_to_text(audio_data, audio_format)

        if text:
            return jsonify({"status": "success", "text": text})
        else:
            return jsonify({"status": "error", "message": "Speech recognition failed"})

    except Exception as e:
        logger.exception("Speech to text error")
        return jsonify({"status": "error", "message": str(e)})


@app.route('/text-to-speech', methods=['POST'])
def text_to_speech():
    """文字转语音"""
    try:
        data = request.json
        if not data or "text" not in data:
            return jsonify({"status": "error", "message": "Missing text"})

        text = data["text"]
        if len(text) > 1000:  # 限制文本长度
            text = text[:1000] + "..."

        # 文字转语音
        audio_data = speech_service.text_to_speech(text)

        if audio_data:
            # 保存临时音频文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_path = temp_file.name

            return send_file(temp_path, as_attachment=True, download_name='response.wav')
        else:
            return jsonify({"status": "error", "message": "Text to speech failed"})

    except Exception as e:
        logger.exception("Text to speech error")
        return jsonify({"status": "error", "message": str(e)})


# 会话管理API
@app.route('/session/info', methods=['GET'])
def get_session_info():
    """获取当前会话信息"""
    try:
        session_id = session.get("session_id")
        if not session_id:
            return jsonify({"status": "error", "message": "No active session"})

        info = redis_manager.get_session_info(session_id)
        history = redis_manager.get_session(session_id)

        return jsonify({
            "status": "success",
            "session_id": session_id,
            "info": info,
            "history_length": len(history)
        })
    except Exception as e:
        logger.exception("Get session info error")
        return jsonify({"status": "error", "message": str(e)})


@app.route('/session/clear', methods=['POST'])
def clear_session():
    """清除当前会话"""
    try:
        session_id = session.get("session_id")
        if session_id:
            redis_manager.clear_session(session_id)
            session.pop("session_id", None)

        return jsonify({"status": "success", "message": "Session cleared"})
    except Exception as e:
        logger.exception("Clear session error")
        return jsonify({"status": "error", "message": str(e)})


@app.route('/session/history', methods=['GET'])
def get_session_history():
    """获取会话历史"""
    try:
        session_id = session.get("session_id")
        if not session_id:
            return jsonify({"status": "success", "history": []})

        history = redis_manager.get_session(session_id)
        return jsonify({"status": "success", "history": history})
    except Exception as e:
        logger.exception("Get session history error")
        return jsonify({"status": "error", "message": str(e)})


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)