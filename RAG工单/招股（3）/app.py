import os, uuid, logging, time, threading
from flask import Flask, request, jsonify, render_template, session, send_file
from flask_cors import CORS
from file_processor import allowed_file, process_uploaded_file
from vector_search import search_similar, search_with_filters
from llm_client import generate_answer, init_llm_client
from embeddings import generate_embeddings, init_embedding_model
from milvus_manager import init_milvus, store_vectors
from reranker import rerank_documents
from redis_manager import redis_manager
from speech_service import speech_service
import tempfile
import base64
from datetime import datetime

app = Flask(__name__)
CORS(app)
app.secret_key = os.urandom(24)

UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'pdf'}
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

collection = None
llm_client = None

# 全局进度存储
upload_progress = {}
progress_lock = threading.Lock()

def initialize_application():
    global collection, llm_client
    try:
        logger.info("Initializing components...")
        collection = init_milvus()
        init_embedding_model()
        llm_client = init_llm_client()
        logger.info("All components initialized.")
        return True
    except Exception as e:
        logger.error(f"Init failed: {e}", exc_info=True)
        return False

initialize_application()

@app.route('/')
def index():
    session.setdefault("session_id", str(uuid.uuid4()))
    return render_template('index.html')

def update_progress(upload_id, progress_data):
    """更新上传进度"""
    with progress_lock:
        upload_progress[upload_id] = progress_data


@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({"status": "error", "message": "No file part"})

    file = request.files['file']
    if file.filename == '':
        return jsonify({"status": "error", "message": "No selected file"})

    if not (file and allowed_file(file.filename, ALLOWED_EXTENSIONS)):
        return jsonify({"status": "error", "message": "Unsupported file type"})

    # 生成上传ID
    upload_id = str(uuid.uuid4())

    # 初始化进度
    initial_progress = {
        "upload_id": upload_id,
        "filename": file.filename,
        "status": "starting",
        "progress_percentage": 0,
        "current_step": "准备处理文件",
        "start_time": datetime.now().isoformat(),
        "steps": []
    }

    with progress_lock:
        upload_progress[upload_id] = initial_progress

    def progress_callback(progress_data):
        """进度回调函数"""
        update_progress(upload_id, {
            **initial_progress,
            "status": "processing",
            "progress_percentage": progress_data.get("progress_percentage", 0),
            "current_step": f"步骤 {progress_data.get('current_step', 0) + 1}/{progress_data.get('total_steps', 0)}",
            "steps": progress_data.get("steps", []),
            "elapsed_time": progress_data.get("elapsed_time", 0)
        })

    def process_file_async():
        """异步处理文件"""
        try:
            # 处理文件
            result = process_uploaded_file(
                file, UPLOAD_FOLDER, collection, generate_embeddings, progress_callback
            )

            # 更新最终状态
            final_progress = {
                **upload_progress.get(upload_id, {}),
                "status": "completed" if result["status"] == "success" else "failed",
                "progress_percentage": 100,
                "current_step": "处理完成",
                "end_time": datetime.now().isoformat(),
                "result": result
            }

            update_progress(upload_id, final_progress)

        except Exception as e:
            logger.exception("Upload processing error")
            error_progress = {
                **upload_progress.get(upload_id, {}),
                "status": "failed",
                "current_step": "处理失败",
                "error": str(e),
                "end_time": datetime.now().isoformat()
            }
            update_progress(upload_id, error_progress)

    # 启动异步处理
    thread = threading.Thread(target=process_file_async)
    thread.daemon = True
    thread.start()

    return jsonify({
        "status": "success",
        "message": "文件上传成功，开始处理",
        "upload_id": upload_id
    })


@app.route('/upload/progress/<upload_id>', methods=['GET'])
def get_upload_progress(upload_id):
    """获取上传进度"""
    with progress_lock:
        progress_data = upload_progress.get(upload_id)

    if not progress_data:
        return jsonify({"status": "error", "message": "Upload ID not found"})

    return jsonify({
        "status": "success",
        "progress": progress_data
    })


@app.route('/upload/list', methods=['GET'])
def list_uploads():
    """列出所有上传任务"""
    with progress_lock:
        uploads = list(upload_progress.values())

    # 按时间排序，最新的在前
    uploads.sort(key=lambda x: x.get("start_time", ""), reverse=True)

    return jsonify({
        "status": "success",
        "uploads": uploads[:20]  # 只返回最近20个
    })

@app.route('/query', methods=['POST'])
def handle_query():
    data = request.json
    if not data or "query" not in data:
        return jsonify({"status": "error", "message": "Missing query"})

    session_id = session.setdefault("session_id", str(uuid.uuid4()))
    chat_history = redis_manager.get_session(session_id)
    query = data["query"]
    content_type = data.get("content_type", "all")  # 支持内容类型过滤
    start = time.time()

    try:
        # 使用增强的混合检索
        initial = search_with_filters(query, collection, generate_embeddings, content_type, top_k=20)
        if not initial:
            no_result_msg = "未找到相关信息，请先上传文档。"
            new_hist = chat_history + [
                {"role": "user", "content": query},
                {"role": "assistant", "content": no_result_msg}
            ]
            redis_manager.store_session(session_id, new_hist)
            return jsonify({
                "status": "success",
                "answer": no_result_msg,
                "references": [],
                "session_info": redis_manager.get_session_info(session_id)
            })

        # 重排序
        reranked = rerank_documents(query, initial, top_n=6)

        # 生成答案（包含对话历史）
        answer = generate_answer(query, reranked, llm_client, chat_history)

        # 更新对话历史
        new_hist = chat_history + [
            {"role": "user", "content": query},
            {"role": "assistant", "content": answer}
        ]
        redis_manager.store_session(session_id, new_hist)

        latency = round((time.time() - start), 2)

        # 增强的参考信息
        references = []
        for r in reranked:
            ref_info = {
                "text": r["text"][:500] + "..." if len(r["text"]) > 500 else r["text"],
                "score": round(r["score"], 3),
                "type": "table" if "表格内容：" in r["text"] else
                        "image" if "图片" in r["text"] and "内容：" in r["text"] else "text"
            }
            if "vector_score" in r:
                ref_info["vector_score"] = round(r["vector_score"], 3)
                ref_info["keyword_score"] = round(r["keyword_score"], 3)
            references.append(ref_info)

        return jsonify({
            "status": "success",
            "answer": answer,
            "latency": latency,
            "references": references,
            "session_info": redis_manager.get_session_info(session_id),
            "search_stats": {
                "initial_results": len(initial),
                "reranked_results": len(reranked),
                "content_type": content_type
            }
        })
    except Exception as e:
        logger.exception("Query error")
        return jsonify({"status": "error", "message": str(e)})

# 语音相关API
@app.route('/speech-to-text', methods=['POST'])
def speech_to_text():
    """语音转文字"""
    try:
        if 'audio' not in request.files:
            return jsonify({"status": "error", "message": "No audio file"})

        audio_file = request.files['audio']
        audio_format = request.form.get('format', 'wav')

        # 读取音频数据
        audio_data = audio_file.read()

        # 验证音频格式
        if not speech_service.validate_audio_format(audio_data, audio_format):
            return jsonify({"status": "error", "message": "Invalid audio format"})

        # 语音转文字
        text = speech_service.speech_to_text(audio_data, audio_format)

        if text:
            return jsonify({"status": "success", "text": text})
        else:
            return jsonify({"status": "error", "message": "Speech recognition failed"})

    except Exception as e:
        logger.exception("Speech to text error")
        return jsonify({"status": "error", "message": str(e)})


@app.route('/text-to-speech', methods=['POST'])
def text_to_speech():
    """文字转语音"""
    try:
        data = request.json
        if not data or "text" not in data:
            return jsonify({"status": "error", "message": "Missing text"})

        text = data["text"]
        if len(text) > 1000:  # 限制文本长度
            text = text[:1000] + "..."

        # 文字转语音
        audio_data = speech_service.text_to_speech(text)

        if audio_data:
            # 保存临时音频文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_path = temp_file.name

            return send_file(temp_path, as_attachment=True, download_name='response.wav')
        else:
            return jsonify({"status": "error", "message": "Text to speech failed"})

    except Exception as e:
        logger.exception("Text to speech error")
        return jsonify({"status": "error", "message": str(e)})


# 会话管理API
@app.route('/session/info', methods=['GET'])
def get_session_info():
    """获取当前会话信息"""
    try:
        session_id = session.get("session_id")
        if not session_id:
            return jsonify({"status": "error", "message": "No active session"})

        info = redis_manager.get_session_info(session_id)
        history = redis_manager.get_session(session_id)

        return jsonify({
            "status": "success",
            "session_id": session_id,
            "info": info,
            "history_length": len(history)
        })
    except Exception as e:
        logger.exception("Get session info error")
        return jsonify({"status": "error", "message": str(e)})


@app.route('/session/clear', methods=['POST'])
def clear_session():
    """清除当前会话"""
    try:
        session_id = session.get("session_id")
        if session_id:
            redis_manager.clear_session(session_id)
            session.pop("session_id", None)

        return jsonify({"status": "success", "message": "Session cleared"})
    except Exception as e:
        logger.exception("Clear session error")
        return jsonify({"status": "error", "message": str(e)})


@app.route('/session/history', methods=['GET'])
def get_session_history():
    """获取会话历史"""
    try:
        session_id = session.get("session_id")
        if not session_id:
            return jsonify({"status": "success", "history": []})

        history = redis_manager.get_session(session_id)
        return jsonify({"status": "success", "history": history})
    except Exception as e:
        logger.exception("Get session history error")
        return jsonify({"status": "error", "message": str(e)})


@app.route('/database/stats', methods=['GET'])
def get_database_stats():
    """获取数据库统计信息"""
    try:
        from milvus_manager import get_collection_stats

        if not collection:
            return jsonify({"status": "error", "message": "数据库未初始化"})

        stats = get_collection_stats(collection)

        # 获取Redis统计信息
        redis_info = {
            "active_sessions": len(redis_manager.get_active_sessions()),
            "connection_status": "connected"
        }

        return jsonify({
            "status": "success",
            "milvus": stats,
            "redis": redis_info,
            "system_info": {
                "upload_folder": UPLOAD_FOLDER,
                "allowed_extensions": list(ALLOWED_EXTENSIONS),
                "server_time": datetime.now().isoformat()
            }
        })
    except Exception as e:
        logger.exception("Get database stats error")
        return jsonify({"status": "error", "message": str(e)})


@app.route('/system/health', methods=['GET'])
def health_check():
    """系统健康检查"""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {}
        }

        # 检查Milvus
        try:
            if collection:
                collection.load()
                health_status["components"]["milvus"] = {
                    "status": "healthy",
                    "entities": collection.num_entities
                }
            else:
                health_status["components"]["milvus"] = {
                    "status": "not_initialized"
                }
        except Exception as e:
            health_status["components"]["milvus"] = {
                "status": "unhealthy",
                "error": str(e)
            }

        # 检查Redis
        try:
            redis_manager.connection.ping()
            health_status["components"]["redis"] = {
                "status": "healthy",
                "active_sessions": len(redis_manager.get_active_sessions())
            }
        except Exception as e:
            health_status["components"]["redis"] = {
                "status": "unhealthy",
                "error": str(e)
            }

        # 检查LLM客户端
        try:
            if llm_client:
                health_status["components"]["llm"] = {"status": "healthy"}
            else:
                health_status["components"]["llm"] = {"status": "not_initialized"}
        except Exception as e:
            health_status["components"]["llm"] = {
                "status": "unhealthy",
                "error": str(e)
            }

        # 检查嵌入模型
        try:
            from embeddings import embedding_model
            if embedding_model:
                health_status["components"]["embedding"] = {"status": "healthy"}
            else:
                health_status["components"]["embedding"] = {"status": "not_initialized"}
        except Exception as e:
            health_status["components"]["embedding"] = {
                "status": "unhealthy",
                "error": str(e)
            }

        # 检查整体状态
        unhealthy_components = [
            name for name, comp in health_status["components"].items()
            if comp.get("status") == "unhealthy"
        ]

        if unhealthy_components:
            health_status["status"] = "degraded"
            health_status["unhealthy_components"] = unhealthy_components

        return jsonify(health_status)

    except Exception as e:
        logger.exception("Health check error")
        return jsonify({
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        })


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)