import os, uuid, logging, time
from flask import Flask, request, jsonify, render_template, session
from flask_cors import CORS
from file_processor import allowed_file, process_uploaded_file
from vector_search import search_similar
from llm_client import generate_answer, init_llm_client
from embeddings import generate_embeddings, init_embedding_model
from milvus_manager import init_milvus, store_vectors
from reranker import rerank_documents
from redis_manager import redis_manager

app = Flask(__name__)
CORS(app)
app.secret_key = os.urandom(24)

UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'pdf'}
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

collection = None
llm_client = None

def initialize_application():
    global collection, llm_client
    try:
        logger.info("Initializing components...")
        collection = init_milvus()
        init_embedding_model()
        llm_client = init_llm_client()
        logger.info("All components initialized.")
        return True
    except Exception as e:
        logger.error(f"Init failed: {e}", exc_info=True)
        return False

initialize_application()

@app.route('/')
def index():
    session.setdefault("session_id", str(uuid.uuid4()))
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({"status": "error", "message": "No file part"})
    file = request.files['file']
    if file.filename == '':
        return jsonify({"status": "error", "message": "No selected file"})
    if file and allowed_file(file.filename, ALLOWED_EXTENSIONS):
        try:
            res = process_uploaded_file(file, UPLOAD_FOLDER, collection, generate_embeddings)
            return jsonify(res)
        except Exception as e:
            logger.exception("Upload error")
            return jsonify({"status": "error", "message": str(e)})
    return jsonify({"status": "error", "message": "Unsupported file type"})

@app.route('/query', methods=['POST'])
def handle_query():
    data = request.json
    if not data or "query" not in data:
        return jsonify({"status": "error", "message": "Missing query"})

    session_id = session.setdefault("session_id", str(uuid.uuid4()))
    chat_history = redis_manager.get_session(session_id)
    query = data["query"]
    start = time.time()

    try:
        initial = search_similar(query, collection, generate_embeddings, top_k=15)
        if not initial:
            new_hist = chat_history + [
                {"role": "user", "content": query},
                {"role": "assistant", "content": "未找到相关信息，请先上传文档。"}
            ]
            redis_manager.store_session(session_id, new_hist)
            return jsonify({"status": "success", "answer": "未找到相关信息，请先上传文档。", "references": []})

        reranked = rerank_documents(query, initial, top_n=5)
        answer = generate_answer(query, reranked, llm_client, chat_history)

        new_hist = chat_history + [
            {"role": "user", "content": query},
            {"role": "assistant", "content": answer}
        ]
        redis_manager.store_session(session_id, new_hist)

        latency = round((time.time() - start), 2)
        references = [{"text": r["text"], "score": r["score"]} for r in reranked]
        return jsonify({
            "status": "success",
            "answer": answer,
            "latency": latency,
            "references": references
        })
    except Exception as e:
        logger.exception("Query error")
        return jsonify({"status": "error", "message": str(e)})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)