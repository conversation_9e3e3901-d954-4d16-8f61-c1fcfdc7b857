<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>智能文档问答系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <style>
    .accuracy-bar{height:6px;border-radius:3px;background:#e5e7eb;overflow:hidden}
    .accuracy-fill{height:100%;background:#f59e0b}
  </style>
</head>
<body class="bg-gray-50 font-sans min-h-screen">
<header class="bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg sticky top-0 z-10">
  <div class="container mx-auto px-4 py-4 flex items-center justify-between">
    <div class="flex items-center space-x-3">
      <i class="fa fa-file-text-o text-xl"></i>
      <h1 class="text-xl font-bold">智能文档问答系统</h1>
    </div>
    <div class="text-xs opacity-80">基于BGE-M3 & HNSW & Milvus</div>
  </div>
</header>

<main class="container mx-auto px-4 py-8 max-w-4xl">
  <!-- 上传 -->
  <section class="bg-white rounded-xl shadow p-6 mb-8">
    <h2 class="text-lg font-semibold mb-2"><i class="fa fa-cloud-upload mr-2"></i>上传文档</h2>
    <p class="text-sm text-gray-500 mb-4">支持 PDF / TXT</p>
    <div id="drop-area" class="border-2 border-dashed rounded-xl p-8 text-center hover:border-blue-500 transition">
      <i class="fa fa-file-text-o text-3xl text-gray-400 mb-2"></i>
      <p>拖放文件或 <label class="text-blue-600 underline cursor-pointer"><input id="file-input" type="file" class="hidden" accept=".pdf,.txt">选择文件</label></p>
    </div>
    <div id="upload-status" class="mt-3 hidden"></div>
  </section>

  <!-- 查询 -->
  <section class="bg-white rounded-xl shadow p-6">
    <h2 class="text-lg font-semibold mb-2"><i class="fa fa-search mr-2"></i>文档查询</h2>
    <div class="relative">
      <input id="query-input" class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="输入问题..."/>
      <button id="search-button" class="absolute right-2 top-1/2 -translate-y-1/2 bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600">查询</button>
    </div>
    <div id="query-status" class="mt-3 hidden"><i class="fa fa-spinner fa-spin mr-1"></i>正在查询...</div>

    <!-- 答案 -->
    <div id="answer-container" class="mt-6 hidden">
      <h3 class="font-semibold mb-2">答案：</h3>
      <div id="answer-content" class="prose max-w-none bg-gray-50 p-4 rounded-lg"></div>
      <div class="text-right text-sm text-gray-500 mt-1">
        耗时：<span id="latency">0</span> 秒
      </div>
    </div>

    <!-- 参考 -->
    <div id="reference-container" class="mt-6 hidden">
      <h3 class="font-semibold mb-2"><i class="fa fa-file-text-o mr-1"></i>参考文档</h3>
      <div id="reference-list" class="space-y-3"></div>
    </div>
  </section>
</main>

<footer class="text-center text-sm text-gray-500 py-4">
  © 2025 智能文档问答系统 · 本地AI · 安全可信
</footer>

<script>
/* 上传 */
const dropArea = document.getElementById('drop-area');
const fileInput = document.getElementById('file-input');
const uploadStatus = document.getElementById('upload-status');
['dragenter','dragover','dragleave','drop'].forEach(evt=>{
  dropArea.addEventListener(evt, e=>e.preventDefault());
});
['dragenter','dragover'].forEach(evt=>dropArea.addEventListener(evt,()=>dropArea.classList.add('border-blue-500')));
['dragleave','drop'].forEach(evt=>dropArea.addEventListener(evt,()=>dropArea.classList.remove('border-blue-500')));
dropArea.addEventListener('drop',e=>handleFiles(e.dataTransfer.files));
fileInput.addEventListener('change',()=>handleFiles(fileInput.files));

function handleFiles(files){
  if(!files.length) return;
  const file=files[0];
  const form=new FormData();
  form.append('file',file);
  uploadStatus.className='text-sm mt-2';
  uploadStatus.innerHTML='<i class="fa fa-spinner fa-spin mr-1"></i>上传中...';
  fetch('/upload',{method:'POST',body:form})
    .then(r=>r.json())
    .then(d=>{
      uploadStatus.innerHTML = d.status==='success'
        ? '<i class="fa fa-check text-green-500 mr-1"></i>'+d.message
        : '<i class="fa fa-times text-red-500 mr-1"></i>'+d.message;
    });
}

/* 查询 */
const queryInput=document.getElementById('query-input');
const searchBtn=document.getElementById('search-button');
const queryStatus=document.getElementById('query-status');
const answerContainer=document.getElementById('answer-container');
const answerContent=document.getElementById('answer-content');
const latencySpan=document.getElementById('latency');
const refContainer=document.getElementById('reference-container');
const refList=document.getElementById('reference-list');

searchBtn.onclick = () => doQuery();
queryInput.addEventListener('keydown',e=>{if(e.key==='Enter') doQuery();});

function doQuery(){
  const q=queryInput.value.trim();
  if(!q) return;
  queryStatus.classList.remove('hidden');
  answerContainer.classList.add('hidden');
  refContainer.classList.add('hidden');

  fetch('/query',{
    method:'POST',
    headers:{'Content-Type':'application/json'},
    body:JSON.stringify({query:q})
  })
  .then(r=>r.json())
  .then(d=>{
    queryStatus.classList.add('hidden');
    if(d.status==='success'){
      answerContent.innerHTML = d.answer;
      latencySpan.textContent = d.latency || 0;
      answerContainer.classList.remove('hidden');

      if(d.references && d.references.length){
        refList.innerHTML='';
        d.references.forEach(r=>{
          const score = (r.score*100).toFixed(1);
          const div=document.createElement('div');
          div.className='bg-gray-50 p-3 rounded-lg border';
          div.innerHTML=`
            <div class="text-xs font-bold text-blue-600">相关度 ${score}%</div>
            <div class="accuracy-bar my-1"><div class="accuracy-fill" style="width:${score}%"></div></div>
            <p class="text-sm text-gray-700 whitespace-pre-wrap">${r.text}</p>
          `;
          refList.appendChild(div);
        });
        refContainer.classList.remove('hidden');
      }
    }else{
      answerContent.innerHTML = '<p class="text-red-500">'+d.message+'</p>';
      answerContainer.classList.remove('hidden');
    }
  });
}
</script>
</body>
</html>