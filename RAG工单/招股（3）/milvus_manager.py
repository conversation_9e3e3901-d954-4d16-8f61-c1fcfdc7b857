from pymilvus import (
    connections, FieldSchema, CollectionSchema, DataType,
    Collection, utility
)
import logging

COLLECTION_NAME = "document_vectors"
DIM = 1024
MILVUS_HOST, MILVUS_PORT = "localhost", 19530

def init_milvus() -> Collection:
    connections.connect("default", host=MILVUS_HOST, port=MILVUS_PORT)

    # 如果存在旧集合且 metric 不对，直接删掉重建
    if utility.has_collection(COLLECTION_NAME):
        col = Collection(COLLECTION_NAME)
        idx = col.index()
        if idx.params.get("metric_type") != "IP":
            logging.warning("旧集合 metric 不是 IP，删除后重建...")
            col.drop()
            utility.drop_collection(COLLECTION_NAME)
        else:
            col.load()
            return col

    # 1. schema
    fields = [
        FieldSchema("id", DataType.INT64, is_primary=True, auto_id=True),
        FieldSchema("text", DataType.VARCHAR, max_length=65_535),
        FieldSchema("vector", DataType.FLOAT_VECTOR, dim=DIM)
    ]
    schema = CollectionSchema(fields, "Document vectors")
    col = Collection(COLLECTION_NAME, schema)

    # 2. HNSW + IP
    index_params = {
        "index_type": "HNSW",
        "metric_type": "IP",
        "params": {"M": 32, "efConstruction": 200}
    }
    col.create_index("vector", index_params)
    logging.info("HNSW-IP index created.")
    col.load()
    return col


def store_vectors(texts, embeddings, collection: Collection):
    try:
        data = [texts, embeddings]
        mr = collection.insert(data)
        collection.flush()
        logging.info(f"Stored {len(texts)} vectors.")
        return {"status": "success", "count": len(texts)}
    except Exception as e:
        logging.error(f"Store vectors error: {e}")
        return {"status": "error", "message": str(e)}