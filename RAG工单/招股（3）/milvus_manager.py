from pymilvus import (
    connections, FieldSchema, CollectionSchema, DataType,
    Collection, utility
)
import logging

COLLECTION_NAME = "document_vectors"
DIM = 1024
MILVUS_HOST, MILVUS_PORT = "localhost", 19530

def init_milvus() -> Collection:
    connections.connect("default", host=MILVUS_HOST, port=MILVUS_PORT)

    # 如果存在旧集合且 metric 不对，直接删掉重建
    if utility.has_collection(COLLECTION_NAME):
        col = Collection(COLLECTION_NAME)
        idx = col.index()
        if idx.params.get("metric_type") != "IP":
            logging.warning("旧集合 metric 不是 IP，删除后重建...")
            col.drop()
            utility.drop_collection(COLLECTION_NAME)
        else:
            col.load()
            return col

    # 1. schema
    fields = [
        FieldSchema("id", DataType.INT64, is_primary=True, auto_id=True),
        FieldSchema("text", DataType.VARCHAR, max_length=65_535),
        FieldSchema("vector", DataType.FLOAT_VECTOR, dim=DIM)
    ]
    schema = CollectionSchema(fields, "Document vectors")
    col = Collection(COLLECTION_NAME, schema)

    # 2. 优化的HNSW + IP索引
    index_params = {
        "index_type": "HNSW",
        "metric_type": "IP",
        "params": {
            "M": 48,  # 增加连接数，提高召回率
            "efConstruction": 400  # 增加构建时的搜索深度
        }
    }
    col.create_index("vector", index_params)
    logging.info("Optimized HNSW-IP index created with M=48, efConstruction=400")

    # 加载集合并设置搜索参数
    col.load()
    logging.info(f"Collection {COLLECTION_NAME} loaded successfully")
    return col


def store_vectors(texts, embeddings, collection: Collection):
    """存储文本和向量到Milvus数据库"""
    try:
        import numpy as np

        # 确保embeddings是正确的格式
        if isinstance(embeddings, list):
            embeddings = np.array(embeddings, dtype=np.float32)
        elif isinstance(embeddings, np.ndarray):
            embeddings = embeddings.astype(np.float32)

        # 验证数据维度
        if len(texts) != len(embeddings):
            raise ValueError(f"文本数量({len(texts)})与向量数量({len(embeddings)})不匹配")

        if len(embeddings) == 0:
            raise ValueError("没有向量数据需要存储")

        # 验证向量维度
        expected_dim = DIM
        actual_dim = embeddings.shape[1] if len(embeddings.shape) > 1 else len(embeddings[0])
        if actual_dim != expected_dim:
            raise ValueError(f"向量维度不匹配: 期望{expected_dim}, 实际{actual_dim}")

        # 准备数据
        data = [texts, embeddings.tolist()]

        logging.info(f"开始存储 {len(texts)} 个向量到Milvus...")
        logging.info(f"向量维度: {actual_dim}, 文本样例: {texts[0][:100]}...")

        # 插入数据
        mr = collection.insert(data)

        # 强制刷新确保数据持久化
        collection.flush()

        # 验证插入结果
        if hasattr(mr, 'insert_count'):
            inserted_count = mr.insert_count
        else:
            inserted_count = len(texts)

        # 获取集合统计信息
        collection.load()  # 确保集合已加载
        stats = collection.query(
            expr="",
            output_fields=["count(*)"],
            consistency_level="Strong"
        )

        total_entities = collection.num_entities

        logging.info(f"成功存储 {inserted_count} 个向量")
        logging.info(f"集合总实体数: {total_entities}")

        return {
            "status": "success",
            "count": inserted_count,
            "total_entities": total_entities,
            "vector_dimension": actual_dim,
            "collection_name": collection.name
        }

    except Exception as e:
        error_msg = f"存储向量失败: {str(e)}"
        logging.error(error_msg, exc_info=True)
        return {
            "status": "error",
            "message": error_msg,
            "error_type": type(e).__name__
        }


def get_collection_stats(collection: Collection):
    """获取集合统计信息"""
    try:
        collection.load()
        stats = {
            "name": collection.name,
            "num_entities": collection.num_entities,
            "schema": {
                "fields": [
                    {
                        "name": field.name,
                        "type": field.dtype.name,
                        "is_primary": field.is_primary,
                        "auto_id": field.auto_id if hasattr(field, 'auto_id') else False
                    }
                    for field in collection.schema.fields
                ]
            }
        }

        # 获取索引信息
        try:
            indexes = collection.indexes
            stats["indexes"] = [
                {
                    "field_name": idx.field_name,
                    "index_type": idx.params.get("index_type"),
                    "metric_type": idx.params.get("metric_type"),
                    "params": idx.params
                }
                for idx in indexes
            ]
        except Exception as idx_error:
            logging.warning(f"获取索引信息失败: {idx_error}")
            stats["indexes"] = []

        return stats

    except Exception as e:
        logging.error(f"获取集合统计信息失败: {e}")
        return {"error": str(e)}