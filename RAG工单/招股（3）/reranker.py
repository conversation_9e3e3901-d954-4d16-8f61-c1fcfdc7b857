import os
import logging
from typing import List, Dict, Optional
import numpy as np
from sentence_transformers import CrossEncoder

LOCAL_RERANK_MODEL_PATH = r"D:\model\BAAI\bge-reranker-v2-m3"
rerank_model = None

def rerank_documents(
    query: str,
    documents: List[Dict],
    top_n: int = 3,
    model_name: Optional[str] = None
) -> List[Dict]:
    global rerank_model

    if not documents:
        return []

    if rerank_model is None:
        try:
            logging.info("Loading local rerank model...")
            rerank_model = CrossEncoder(LOCAL_RERANK_MODEL_PATH)
            logging.info("Rerank model loaded.")
        except Exception as e:
            logging.error(f"Load rerank model failed: {e}")
            return documents[:top_n]

    try:
        pairs = [(query, doc["text"]) for doc in documents]
        scores = rerank_model.predict(pairs)

        for i, doc in enumerate(documents):
            doc["rerank_score"] = float(scores[i])
            doc["score"] = doc.get("score", 0)   # 保留向量分数，前端可用

        reranked = sorted(documents, key=lambda x: x["rerank_score"], reverse=True)
        return reranked[:top_n]
    except Exception as e:
        logging.error(f"Rerank failed: {e}")
        return documents[:top_n]