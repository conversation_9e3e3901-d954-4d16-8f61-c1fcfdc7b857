#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文档问答系统测试脚本
"""

import os
import sys
import time
import requests
import json
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SystemTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_server_status(self):
        """测试服务器状态"""
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                logger.info("✓ 服务器运行正常")
                return True
            else:
                logger.error(f"✗ 服务器响应异常: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"✗ 无法连接服务器: {e}")
            return False
    
    def test_file_upload(self, test_file_path=None):
        """测试文件上传功能"""
        if not test_file_path:
            # 创建测试文件
            test_content = """
            这是一个测试文档。
            
            第一章：系统介绍
            本系统是一个智能文档问答系统，支持PDF和TXT文件的上传和处理。
            
            第二章：功能特性
            1. 多轮对话支持
            2. 语音交互功能
            3. 混合检索算法
            4. PDF处理增强
            
            表格内容：
            项目    数量    价格
            产品A   100     1000
            产品B   200     2000
            
            总结：系统功能完善，性能优异。
            """
            
            test_file_path = "test_document.txt"
            with open(test_file_path, 'w', encoding='utf-8') as f:
                f.write(test_content)
        
        try:
            with open(test_file_path, 'rb') as f:
                files = {'file': (test_file_path, f, 'text/plain')}
                response = self.session.post(f"{self.base_url}/upload", files=files)
                
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    logger.info(f"✓ 文件上传成功: {data.get('message')}")
                    logger.info(f"  分块数量: {data.get('chunks_count')}")
                    return True
                else:
                    logger.error(f"✗ 文件上传失败: {data.get('message')}")
                    return False
            else:
                logger.error(f"✗ 上传请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"✗ 文件上传异常: {e}")
            return False
        finally:
            # 清理测试文件
            if os.path.exists(test_file_path) and test_file_path == "test_document.txt":
                os.remove(test_file_path)
    
    def test_query(self, query="这个文档主要讲什么？"):
        """测试查询功能"""
        try:
            data = {
                "query": query,
                "content_type": "all"
            }
            
            response = self.session.post(
                f"{self.base_url}/query",
                json=data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    logger.info(f"✓ 查询成功")
                    logger.info(f"  问题: {query}")
                    logger.info(f"  回答: {result.get('answer', '')[:100]}...")
                    logger.info(f"  响应时间: {result.get('latency')}秒")
                    logger.info(f"  参考文档数: {len(result.get('references', []))}")
                    return True
                else:
                    logger.error(f"✗ 查询失败: {result.get('message')}")
                    return False
            else:
                logger.error(f"✗ 查询请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"✗ 查询异常: {e}")
            return False
    
    def test_session_management(self):
        """测试会话管理"""
        try:
            # 获取会话信息
            response = self.session.get(f"{self.base_url}/session/info")
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    logger.info(f"✓ 会话信息获取成功")
                    logger.info(f"  会话ID: {data.get('session_id', '')[:8]}...")
                    logger.info(f"  历史长度: {data.get('history_length', 0)}")
                else:
                    logger.warning("会话信息获取失败，可能是新会话")
            
            # 清除会话
            response = self.session.post(f"{self.base_url}/session/clear")
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    logger.info("✓ 会话清除成功")
                    return True
                else:
                    logger.error(f"✗ 会话清除失败: {data.get('message')}")
                    return False
            else:
                logger.error(f"✗ 会话清除请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"✗ 会话管理异常: {e}")
            return False
    
    def test_multi_turn_conversation(self):
        """测试多轮对话"""
        questions = [
            "这个文档有哪些章节？",
            "第二章讲了什么内容？",
            "表格中有什么数据？",
            "产品A和产品B的价格分别是多少？"
        ]
        
        logger.info("开始多轮对话测试...")
        success_count = 0
        
        for i, question in enumerate(questions, 1):
            logger.info(f"\n第{i}轮对话:")
            if self.test_query(question):
                success_count += 1
            time.sleep(1)  # 避免请求过快
        
        success_rate = success_count / len(questions)
        if success_rate >= 0.8:
            logger.info(f"✓ 多轮对话测试通过 ({success_count}/{len(questions)})")
            return True
        else:
            logger.error(f"✗ 多轮对话测试失败 ({success_count}/{len(questions)})")
            return False
    
    def test_content_type_filtering(self):
        """测试内容类型过滤"""
        content_types = ["all", "text", "table", "image"]
        query = "有什么数据？"
        
        logger.info("开始内容类型过滤测试...")
        success_count = 0
        
        for content_type in content_types:
            try:
                data = {
                    "query": query,
                    "content_type": content_type
                }
                
                response = self.session.post(
                    f"{self.base_url}/query",
                    json=data,
                    headers={'Content-Type': 'application/json'}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('status') == 'success':
                        logger.info(f"✓ {content_type}类型过滤成功")
                        success_count += 1
                    else:
                        logger.warning(f"△ {content_type}类型过滤返回: {result.get('message')}")
                else:
                    logger.error(f"✗ {content_type}类型过滤请求失败")
                    
            except Exception as e:
                logger.error(f"✗ {content_type}类型过滤异常: {e}")
        
        if success_count >= len(content_types) * 0.75:
            logger.info(f"✓ 内容类型过滤测试通过 ({success_count}/{len(content_types)})")
            return True
        else:
            logger.error(f"✗ 内容类型过滤测试失败 ({success_count}/{len(content_types)})")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("=" * 60)
        logger.info("智能文档问答系统 - 功能测试")
        logger.info("=" * 60)
        
        tests = [
            ("服务器状态", self.test_server_status),
            ("文件上传", self.test_file_upload),
            ("基础查询", self.test_query),
            ("会话管理", self.test_session_management),
            ("多轮对话", self.test_multi_turn_conversation),
            ("内容过滤", self.test_content_type_filtering),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*20} {test_name} {'='*20}")
            try:
                if test_func():
                    passed_tests += 1
                    logger.info(f"✓ {test_name} 测试通过")
                else:
                    logger.error(f"✗ {test_name} 测试失败")
            except Exception as e:
                logger.error(f"✗ {test_name} 测试异常: {e}")
        
        # 测试结果汇总
        logger.info("\n" + "=" * 60)
        logger.info("测试结果汇总")
        logger.info("=" * 60)
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试: {passed_tests}")
        logger.info(f"失败测试: {total_tests - passed_tests}")
        logger.info(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        if passed_tests == total_tests:
            logger.info("🎉 所有测试通过！系统运行正常")
            return True
        elif passed_tests >= total_tests * 0.8:
            logger.warning("⚠️  大部分测试通过，系统基本正常")
            return True
        else:
            logger.error("❌ 多项测试失败，请检查系统配置")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='智能文档问答系统测试')
    parser.add_argument('--url', default='http://localhost:5000', help='服务器地址')
    parser.add_argument('--test', choices=['all', 'server', 'upload', 'query', 'session'], 
                       default='all', help='测试类型')
    
    args = parser.parse_args()
    
    tester = SystemTester(args.url)
    
    if args.test == 'all':
        success = tester.run_all_tests()
    elif args.test == 'server':
        success = tester.test_server_status()
    elif args.test == 'upload':
        success = tester.test_file_upload()
    elif args.test == 'query':
        success = tester.test_query()
    elif args.test == 'session':
        success = tester.test_session_management()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
