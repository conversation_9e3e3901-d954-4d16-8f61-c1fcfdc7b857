from sentence_transformers import SentenceTransformer
import logging

embedding_model = None

def init_embedding_model():
    global embedding_model
    try:
        embedding_model = SentenceTransformer(r"D:\model\BAAI\bge-m3")
        return embedding_model
    except Exception as e:
        logging.error(f"Embedding init error: {e}")
        raise

def generate_embeddings(texts):
    global embedding_model
    if embedding_model is None:
        init_embedding_model()
    try:
        return embedding_model.encode(texts)
    except Exception as e:
        logging.error(f"Generate embeddings error: {e}")
        raise