# 智能文档问答系统 - 优化版

## 🚀 新功能特性

### 1. 多轮对话支持
- ✅ Redis短期聊天记录存储
- ✅ 上下文感知的对话生成
- ✅ 会话管理（清除、查看历史）
- ✅ 智能对话历史长度控制

### 2. 语音交互功能
- ✅ 语音转文字（Speech-to-Text）
- ✅ 文字转语音（Text-to-Speech）
- ✅ 实时语音录制界面
- ✅ 支持本地和云端语音服务

### 3. 向量数据库优化
- ✅ 混合检索（向量+关键词+语义）
- ✅ 优化HNSW索引参数（M=48, efConstruction=400）
- ✅ 内容类型过滤（文本/表格/图片）
- ✅ 多层次评分机制

### 4. PDF处理增强
- ✅ 智能表格识别和提取
- ✅ 图片OCR识别优化
- ✅ 语义感知文本分块
- ✅ 图像预处理提升识别准确性

### 5. 现代化界面
- ✅ 响应式设计
- ✅ 实时聊天界面
- ✅ 侧边栏功能面板
- ✅ 语音录制模态框
- ✅ 参考文档可视化

## 📋 系统要求

### 环境依赖
- Python 3.8+
- Redis Server
- Milvus 2.3+
- Tesseract OCR

### 硬件建议
- 内存：8GB+
- 存储：10GB+
- GPU：可选（加速推理）

## 🛠️ 安装部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd RAG工单/招股（3）
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 启动服务

#### 启动Redis
```bash
redis-server
```

#### 启动Milvus
```bash
# 使用Docker
docker-compose up -d
```

#### 安装Tesseract OCR
```bash
# Ubuntu/Debian
sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim

# Windows
# 下载并安装 https://github.com/UB-Mannheim/tesseract/wiki
```

### 4. 配置模型路径
确保以下模型已下载到本地：
- BGE-M3嵌入模型
- BGE-Reranker-v2-M3重排序模型

### 5. 启动应用
```bash
python app.py
```

访问：http://localhost:5000

## 🎯 使用指南

### 文档上传
1. 支持PDF和TXT格式
2. 拖拽或点击上传
3. 自动处理表格和图片内容
4. 显示处理详情和分块信息

### 智能问答
1. 输入问题或使用快捷问题
2. 支持语音输入
3. 实时显示回答和参考文档
4. 多轮对话上下文理解

### 内容过滤
- **全部内容**：搜索所有类型内容
- **文本内容**：仅搜索纯文本
- **表格数据**：专注表格信息
- **图片内容**：搜索图片OCR内容

### 语音功能
1. 点击麦克风按钮开始录音
2. 说出问题后点击停止
3. 系统自动识别并查询
4. 可启用语音播报回答

### 会话管理
- 查看当前会话信息
- 清除对话历史
- 自动会话过期管理

## 🔧 配置说明

### Redis配置
```python
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
SESSION_EXPIRE = 1800  # 30分钟
```

### Milvus配置
```python
MILVUS_HOST = "localhost"
MILVUS_PORT = 19530
COLLECTION_NAME = "document_vectors"
```

### 语音服务配置
```python
SPEECH_CONFIG = {
    "stt_service": "local",  # local, baidu
    "tts_service": "local",  # local, baidu
    "sample_rate": 16000,
}
```

## 📊 性能优化

### 向量检索优化
- HNSW索引参数调优
- 混合评分机制
- 内容类型智能过滤

### 文本分块优化
- 语义感知分块
- 保持表格完整性
- 图片内容独立处理

### 缓存策略
- Redis会话缓存
- 模型加载缓存
- 查询结果缓存

## 🐛 故障排除

### 常见问题

1. **Redis连接失败**
   - 检查Redis服务是否启动
   - 确认端口配置正确

2. **Milvus连接失败**
   - 检查Milvus服务状态
   - 确认网络连接

3. **语音功能不可用**
   - 检查浏览器麦克风权限
   - 确认HTTPS环境（生产环境）

4. **OCR识别效果差**
   - 检查Tesseract安装
   - 确认中文语言包

5. **模型加载失败**
   - 检查模型路径配置
   - 确认模型文件完整性

## 📈 系统监控

### 性能指标
- 查询响应时间
- 向量检索准确率
- 会话活跃度
- 文档处理成功率

### 日志监控
- 应用日志：`app.log`
- 错误日志：`error.log`
- 访问日志：`access.log`

## 🔄 版本更新

### v2.0 新增功能
- 多轮对话支持
- 语音交互功能
- 混合检索算法
- PDF处理增强
- 现代化UI界面

### 后续规划
- [ ] 多语言支持
- [ ] 文档版本管理
- [ ] 用户权限系统
- [ ] API接口开放
- [ ] 移动端适配

## 📞 技术支持

如有问题，请联系技术支持团队或提交Issue。

---

© 2025 智能文档问答系统 · 基于本地AI · 安全可信
