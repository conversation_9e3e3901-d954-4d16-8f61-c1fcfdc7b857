#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文档问答系统启动脚本
检查依赖服务并启动应用
"""

import os
import sys
import time
import subprocess
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    logger.info(f"Python版本: {sys.version}")
    return True

def check_redis():
    """检查Redis服务"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        logger.info("✓ Redis服务正常")
        return True
    except Exception as e:
        logger.error(f"✗ Redis连接失败: {e}")
        logger.info("请启动Redis服务: redis-server")
        return False

def check_milvus():
    """检查Milvus服务"""
    try:
        from pymilvus import connections
        connections.connect("default", host="localhost", port=19530)
        logger.info("✓ Milvus服务正常")
        return True
    except Exception as e:
        logger.error(f"✗ Milvus连接失败: {e}")
        logger.info("请启动Milvus服务: docker-compose up -d")
        return False

def check_models():
    """检查模型文件"""
    model_paths = [
        r"D:\model\BAAI\bge-m3",
        r"D:\model\BAAI\bge-reranker-v2-m3"
    ]
    
    all_exists = True
    for path in model_paths:
        if os.path.exists(path):
            logger.info(f"✓ 模型存在: {path}")
        else:
            logger.error(f"✗ 模型不存在: {path}")
            all_exists = False
    
    return all_exists

def check_tesseract():
    """检查Tesseract OCR"""
    try:
        import pytesseract
        # 尝试获取版本信息
        version = pytesseract.get_tesseract_version()
        logger.info(f"✓ Tesseract版本: {version}")
        return True
    except Exception as e:
        logger.error(f"✗ Tesseract不可用: {e}")
        logger.info("请安装Tesseract OCR和中文语言包")
        return False

def check_dependencies():
    """检查Python依赖"""
    required_packages = [
        'flask', 'flask_cors', 'pymilvus', 'sentence_transformers',
        'redis', 'pytesseract', 'PIL', 'cv2', 'jieba', 'openai'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'cv2':
                import cv2
            else:
                __import__(package)
            logger.info(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            logger.error(f"✗ {package}")
    
    if missing_packages:
        logger.error(f"缺少依赖包: {missing_packages}")
        logger.info("请运行: pip install -r requirements.txt")
        return False
    
    return True

def create_directories():
    """创建必要的目录"""
    directories = ['uploads', 'logs']
    for dir_name in directories:
        os.makedirs(dir_name, exist_ok=True)
        logger.info(f"✓ 目录已创建: {dir_name}")

def start_application():
    """启动应用"""
    logger.info("启动智能文档问答系统...")
    
    try:
        # 设置环境变量
        os.environ['FLASK_APP'] = 'app.py'
        os.environ['FLASK_ENV'] = 'development'
        
        # 启动Flask应用
        from app import app
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )
    except Exception as e:
        logger.error(f"启动应用失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("智能文档问答系统 - 启动检查")
    logger.info("=" * 50)
    
    # 检查列表
    checks = [
        ("Python版本", check_python_version),
        ("Python依赖", check_dependencies),
        ("Redis服务", check_redis),
        ("Milvus服务", check_milvus),
        ("AI模型", check_models),
        ("Tesseract OCR", check_tesseract),
    ]
    
    failed_checks = []
    
    for check_name, check_func in checks:
        logger.info(f"\n检查 {check_name}...")
        if not check_func():
            failed_checks.append(check_name)
    
    if failed_checks:
        logger.error(f"\n以下检查失败: {failed_checks}")
        logger.error("请解决上述问题后重新启动")
        return False
    
    # 创建必要目录
    logger.info("\n创建目录...")
    create_directories()
    
    # 启动应用
    logger.info("\n" + "=" * 50)
    logger.info("所有检查通过，启动应用...")
    logger.info("访问地址: http://localhost:5000")
    logger.info("=" * 50)
    
    start_application()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("\n应用已停止")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        sys.exit(1)
